@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";
@import "./typography.css";

:root,
html[data-theme="light"] {
  --background: #fdfdfd;
  --foreground: #282728;
  --accent: #006cac;
  --muted: #e6e6e6;
  --border: #ece9e9;
}

html[data-theme="dark"] {
  --background: #212737;
  --foreground: #eaedf3;
  --accent: #ff6b01;
  --muted: #343f60bf;
  --border: #ab4b08;
}



@layer base {
  * {
    @apply border-border;
    scrollbar-width: auto;
    scrollbar-color: var(--color-muted) transparent;
    outline-color: var(--accent);
    outline-width: 2px;
  }
  html {
    @apply overflow-y-scroll scroll-smooth;
  }
  body {
    @apply flex min-h-svh flex-col bg-background font-mono text-foreground selection:bg-accent selection:bg-opacity-75 selection:text-background;
  }
  a,
  button {
    @apply outline-offset-1 outline-accent focus-visible:no-underline focus-visible:outline-2 focus-visible:outline-dashed;
  }
  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }
  section,
  footer {
    @apply mx-auto max-w-3xl px-4;
  }
}

.active-nav {
  @apply underline decoration-wavy decoration-2 underline-offset-4;
}
