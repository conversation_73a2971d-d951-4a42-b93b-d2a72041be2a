---
import Layout from "@/layouts/Layout.astro";
import Header from "@/components/Header.astro";
import Footer from "@/components/Footer.astro";
import ImageInverter from "@/components/ImageInverter.astro";
import FeedbackForm from "@/components/FeedbackForm.astro";

import { t, preloadTranslations } from "@/i18n/utils";

const currentLanguage = 'en';
await preloadTranslations(currentLanguage, ['common', 'tools']);

const title = await t('site.title', currentLanguage, 'common');
const description = await t('site.description', currentLanguage, 'common');
const imageInverterTitle = await t('image_inverter.title', currentLanguage, 'tools');
const convertBlackWhiteTitle = await t('convert_black_white.title', currentLanguage, 'tools');
const convertBlackWhiteButton = await t('convert_black_white.button_text', currentLanguage, 'tools');
---

<Layout
  title={title}
  description={description}
  language={currentLanguage}
>
  <Header />
  <main id="main-content">
    <div class="mx-auto max-w-7xl px-4 py-8">
      <div class="flex flex-col lg:flex-row lg:gap-8">
        <!-- Main Content -->
        <div class="flex-1">
          <!-- Hero Section -->
          <section class="mb-12 text-center">
            <h1 class="text-4xl font-bold text-accent mb-4">{title}</h1>
            <p class="text-lg text-foreground/80 max-w-2xl mx-auto">
              {description}
            </p>
          </section>

          <!-- Image Inverter Tool -->
          <section class="mb-12">
            <h2 class="text-2xl font-bold text-accent mb-6">{imageInverterTitle}</h2>
            <ImageInverter />
          </section>

          <!-- Convert Black to White Tool -->
          <section class="mb-12">
            <div class="bg-background border border-border rounded-lg p-6">
              <h3 class="text-xl font-bold text-accent mb-4">{convertBlackWhiteTitle}</h3>
              <p class="text-foreground/70 mb-4">
                Transform black images to white backgrounds instantly. Perfect for document processing and design work.
              </p>
              <button class="bg-accent text-background px-6 py-2 rounded-lg hover:bg-accent/90 transition-colors font-medium">
                {convertBlackWhiteButton}
              </button>
            </div>
          </section>

          <!-- Recent Posts Section -->
          <section class="mb-12">
            <h2 class="text-2xl font-bold text-accent mb-6">Recent Posts</h2>
            <div class="grid gap-6 md:grid-cols-2">
              <!-- Posts will be populated here -->
              <div class="bg-background border border-border rounded-lg p-6">
                <h3 class="text-lg font-semibold mb-2">
                  <a href="/en/posts/sample-post" class="hover:text-accent transition-colors">
                    How to Use Image Color Inversion for Accessibility
                  </a>
                </h3>
                <p class="text-foreground/70 text-sm mb-3">
                  Learn how inverting image colors can improve accessibility for users with visual impairments...
                </p>
                <time class="text-xs text-foreground/60">January 15, 2025</time>
              </div>

              <div class="bg-background border border-border rounded-lg p-6">
                <h3 class="text-lg font-semibold mb-2">
                  <a href="/en/posts/image-processing-tips" class="hover:text-accent transition-colors">
                    5 Essential Image Processing Tips
                  </a>
                </h3>
                <p class="text-foreground/70 text-sm mb-3">
                  Discover professional techniques for processing images online with our free tools...
                </p>
                <time class="text-xs text-foreground/60">January 10, 2025</time>
              </div>
            </div>

            <div class="my-8 text-center">
              <a
                href="/en/posts"
                class="inline-flex items-center gap-2 bg-accent text-background px-6 py-2 rounded-lg hover:bg-accent/90 transition-colors font-medium"
              >
                All Posts
                <svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M5 12h14" />
                  <path d="M5 12l6 6" />
                  <path d="M5 12l6 -6" />
                </svg>
              </a>
            </div>
          </section>
        </div>

        <!-- Right Sidebar -->
        <aside class="lg:w-80 lg:flex-shrink-0">
          <div class="lg:sticky lg:top-8">
            <FeedbackForm />
          </div>
        </aside>
      </div>
    </div>
  </main>
  <Footer />
</Layout>
