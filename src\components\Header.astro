---
import Hr from "./Hr.astro";
import LinkButton from "./LinkButton.astro";
import LanguageSwitcher from "./LanguageSwitcher.astro";
import { SITE } from "@/config";
const { pathname } = Astro.url;

// Remove trailing slash from current pathname if exists
const currentPath =
  pathname.endsWith("/") && pathname !== "/" ? pathname.slice(0, -1) : pathname;

const isActive = (path: string) => {
  const currentPathArray = currentPath.split("/").filter(p => p.trim());
  const pathArray = path.split("/").filter(p => p.trim());

  return currentPath === path || currentPathArray[0] === pathArray[0];
};
---

<header>
  <a
    id="skip-to-content"
    href="#main-content"
    class="absolute -top-full left-16 z-50 bg-background px-3 py-2 text-accent backdrop-blur-lg transition-all focus:top-4"
  >
    Skip to content
  </a>
  <div
    id="nav-container"
    class="mx-auto flex max-w-3xl flex-col items-center justify-between sm:flex-row"
  >
    <div
      id="top-nav-wrap"
      class="relative flex w-full items-baseline justify-between bg-background p-4 sm:items-center sm:py-6"
    >
      <a
        href="/"
        class="absolute py-1 text-2xl leading-7 font-semibold whitespace-nowrap sm:static"
      >
        {SITE.title}
      </a>
      <nav
        id="nav-menu"
        class="flex w-full flex-col items-center sm:ml-2 sm:flex-row sm:justify-end sm:space-x-4 sm:py-0"
      >
        <button
          id="menu-btn"
          class="focus-outline self-end p-2 sm:hidden"
          aria-label="Open Menu"
          aria-expanded="false"
          aria-controls="menu-items"
        >
          <svg id="close-icon" class="hidden" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
            <path d="M18 6l-12 12" />
            <path d="M6 6l12 12" />
          </svg>
          <svg id="menu-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
            <path d="M4 8l16 0" />
            <path d="M4 16l16 0" />
            <path d="M10 12l10 0" />
          </svg>
        </button>
        <ul
          id="menu-items"
          class:list={[
            "mt-4 grid w-44 grid-cols-2 place-content-center gap-2",
            "[&>li>a]:block [&>li>a]:px-4 [&>li>a]:py-3 [&>li>a]:text-center [&>li>a]:font-medium [&>li>a]:hover:text-accent sm:[&>li>a]:px-2 sm:[&>li>a]:py-1",
            "hidden",
            "sm:mt-0 sm:ml-0 sm:flex sm:w-auto sm:gap-x-5 sm:gap-y-0",
          ]}
        >
          {
            SITE.showTools && (
              <li class="col-span-2">
                <a href="/tools" class:list={{ "active-nav": isActive("/tools") }}>
                  Tools
                </a>
              </li>
            )
          }
          <li class="col-span-2">
            <a href="/posts" class:list={{ "active-nav": isActive("/posts") }}>
              Posts
            </a>
          </li>
          <li class="col-span-2">
            <a href="/tags" class:list={{ "active-nav": isActive("/tags") }}>
              Tags
            </a>
          </li>
          <li class="col-span-2">
            <a href="/about" class:list={{ "active-nav": isActive("/about") }}>
              About
            </a>
          </li>
          {
            SITE.showArchives && (
              <li class="col-span-2">
                <LinkButton
                  href="/archives"
                  class:list={[
                    "focus-outline flex justify-center p-3 sm:p-1",
                    {
                      "active-nav [&>svg]:stroke-accent": isActive("/archives"),
                    },
                  ]}
                  ariaLabel="archives"
                  title="Archives"
                >
                  <svg class="hidden sm:inline-block" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                    <path d="M3 4m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z" />
                    <path d="M5 8v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-10" />
                    <path d="M10 12l4 0" />
                  </svg>
                  <span class="sm:sr-only">Archives</span>
                </LinkButton>
              </li>
            )
          }
          <li class="col-span-1 flex items-center justify-center">
            <LinkButton
              href="/search"
              class:list={[
                "focus-outline flex p-3 sm:p-1",
                { "[&>svg]:stroke-accent": isActive("/search") },
              ]}
              ariaLabel="search"
              title="Search"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0" />
                <path d="M21 21l-6 -6" />
              </svg>
              <span class="sr-only">Search</span>
            </LinkButton>
          </li>
          {
            SITE.lightAndDarkMode && (
              <li class="col-span-1 flex items-center justify-center">
                <button
                  id="theme-btn"
                  class="focus-outline relative size-12 p-4 sm:size-8 hover:[&>svg]:stroke-accent"
                  title="Toggles light & dark"
                  aria-label="auto"
                  aria-live="polite"
                >
                  <svg class="absolute top-[50%] left-[50%] -translate-[50%] scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                    <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
                  </svg>
                  <svg class="absolute top-[50%] left-[50%] -translate-[50%] scale-0 rotate-90 transition-all dark:scale-100 dark:rotate-0" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                    <path d="M14.828 14.828a4 4 0 1 0 -5.656 -5.656a4 4 0 0 0 5.656 5.656z" />
                    <path d="M6.343 17.657l-1.414 1.414" />
                    <path d="M6.343 6.343l-1.414 -1.414" />
                    <path d="M17.657 6.343l1.414 -1.414" />
                    <path d="M17.657 17.657l1.414 1.414" />
                    <path d="M4 12h-2" />
                    <path d="M12 4v-2" />
                    <path d="M20 12h2" />
                    <path d="M12 20v2" />
                  </svg>
                </button>
              </li>
            )
          }
          <!-- Language Switcher -->
          <li class="col-span-2 sm:col-span-1 flex items-center justify-center">
            <LanguageSwitcher pathname={pathname} class="w-full sm:w-auto" />
          </li>
        </ul>
      </nav>
    </div>
  </div>
  <Hr />
</header>

<script>
  function toggleNav() {
    const menuBtn = document.querySelector("#menu-btn");
    const menuItems = document.querySelector("#menu-items");
    const menuIcon = document.querySelector("#menu-icon");
    const closeIcon = document.querySelector("#close-icon");

    if (!menuBtn || !menuItems || !menuIcon || !closeIcon) return;

    menuBtn.addEventListener("click", () => {
      const openMenu = menuBtn.getAttribute("aria-expanded") === "true";

      menuBtn.setAttribute("aria-expanded", openMenu ? "false" : "true");
      menuBtn.setAttribute("aria-label", openMenu ? "Open Menu" : "Close Menu");

      menuItems.classList.toggle("hidden");
      menuIcon.classList.toggle("hidden");
      closeIcon.classList.toggle("hidden");
    });
  }

  toggleNav();

  // Runs on view transitions navigation
  document.addEventListener("astro:after-swap", toggleNav);
</script>
