---
import FileUpload from "./FileUpload.astro";
---

<div class="image-inverter">
  <!-- Upload Options Section -->
  <div class="mb-8">
    <h2 class="text-xl font-semibold mb-4">Upload or Paste Image</h2>

    <!-- Paste Zone -->
    <div
      id="paste-zone"
      class="mb-6 border-2 border-dashed border-accent/50 rounded-lg p-8 text-center transition-all duration-300 hover:border-accent hover:bg-accent/5 cursor-pointer focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2"
      tabindex="0"
      role="button"
      aria-label="Paste image from clipboard"
    >
      <div class="space-y-4">
        <div class="text-4xl">📋</div>
        <div>
          <p class="text-lg font-medium text-accent">Paste Image Here</p>
          <p class="text-sm text-foreground/70">Press <kbd class="px-2 py-1 bg-muted rounded text-xs font-mono">Ctrl+V</kbd> or <kbd class="px-2 py-1 bg-muted rounded text-xs font-mono">Cmd+V</kbd></p>
        </div>
        <div class="text-xs text-foreground/60">
          Paste screenshots, copied images, or images from other applications
        </div>
      </div>
    </div>

    <!-- Traditional File Upload -->
    <div class="relative">
      <div class="absolute inset-x-0 top-1/2 transform -translate-y-1/2">
        <div class="flex items-center">
          <div class="flex-grow border-t border-border"></div>
          <span class="px-3 text-sm text-foreground/60 bg-background">or</span>
          <div class="flex-grow border-t border-border"></div>
        </div>
      </div>
      <div class="pt-8">
        <FileUpload id="image-upload" onFileSelect="handleImageUpload" />
      </div>
    </div>
  </div>

  <!-- Paste Status -->
  <div id="paste-status" class="mb-4 text-center text-sm hidden">
    <div id="paste-success" class="text-green-600 hidden">
      ✅ Image pasted successfully!
    </div>
    <div id="paste-error" class="text-red-600 hidden">
      ❌ <span id="paste-error-message"></span>
    </div>
  </div>

  <!-- Processing Section -->
  <div id="processing-section" class="hidden">
    <div class="grid gap-6 md:grid-cols-2">
      <!-- Original Image -->
      <div class="space-y-3">
        <h3 class="text-lg font-medium">Original Image</h3>
        <div class="border border-border rounded-lg overflow-hidden bg-muted/20">
          <img id="original-image" class="w-full h-auto max-h-96 object-contain" alt="Original image" />
        </div>
      </div>

      <!-- Processed Image -->
      <div class="space-y-3">
        <h3 class="text-lg font-medium">Processed Image</h3>
        <div class="border border-border rounded-lg overflow-hidden bg-muted/20">
          <canvas id="inverted-canvas" class="w-full h-auto max-h-96"></canvas>
        </div>
      </div>
    </div>

    <!-- Image Processing Controls -->
    <div class="mt-6 space-y-4">
      <!-- Main Processing Buttons -->
      <div class="flex flex-wrap gap-3 justify-center">
        <button
          id="invert-btn"
          class="px-4 py-2 bg-accent text-background rounded-lg hover:bg-accent hover:bg-opacity-90 transition-colors font-medium text-sm"
        >
          Invert Colors
        </button>

        <button
          id="flip-horizontal-btn"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium text-sm"
        >
          Flip Horizontal
        </button>

        <button
          id="flip-vertical-btn"
          class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium text-sm"
        >
          Flip Vertical
        </button>

        <button
          id="download-btn"
          class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors font-medium text-sm"
        >
          Download Image
        </button>
      </div>

      <!-- Reset Button -->
      <div class="flex justify-center">
        <button
          id="reset-btn"
          class="px-6 py-2 border border-border text-foreground rounded-lg hover:bg-muted/20 transition-colors font-medium"
        >
          Upload New Image
        </button>
      </div>
    </div>

    <!-- Processing Status -->
    <div id="processing-status" class="mt-4 text-center text-sm text-foreground/70 hidden">
      Processing image...
    </div>
  </div>
</div>

<script>
  let currentFile: File | null = null;
  let originalImage: HTMLImageElement | null = null;
  let canvas: HTMLCanvasElement | null = null;
  let ctx: CanvasRenderingContext2D | null = null;

  // Clipboard paste functionality
  function initClipboardPaste(pasteZone: HTMLElement) {
    // Global paste event listener
    document.addEventListener('paste', handlePasteEvent);

    // Click on paste zone to focus and enable paste
    pasteZone.addEventListener('click', () => {
      pasteZone.focus();
      showPasteHint();
    });

    // Keyboard event for paste zone
    pasteZone.addEventListener('keydown', (e) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'v') {
        e.preventDefault();
        // The paste event will be handled by the global paste listener
      }
    });

    // Visual feedback for paste zone
    pasteZone.addEventListener('focus', () => {
      pasteZone.classList.add('ring-2', 'ring-accent', 'ring-offset-2');
    });

    pasteZone.addEventListener('blur', () => {
      pasteZone.classList.remove('ring-2', 'ring-accent', 'ring-offset-2');
      hidePasteStatus();
    });
  }

  function handlePasteEvent(e: ClipboardEvent) {
    e.preventDefault();

    const clipboardItems = e.clipboardData?.items;
    if (!clipboardItems) {
      showPasteError('Clipboard access not available');
      return;
    }

    // Look for image items in clipboard
    let imageFound = false;
    for (let i = 0; i < clipboardItems.length; i++) {
      const item = clipboardItems[i];

      if (item.type.startsWith('image/')) {
        imageFound = true;
        const file = item.getAsFile();

        if (file) {
          // Create a more descriptive filename for pasted images
          const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
          const extension = file.type.split('/')[1] || 'png';
          const pastedFile = new File([file], `pasted-image-${timestamp}.${extension}`, {
            type: file.type
          });

          currentFile = pastedFile;
          showPasteSuccess();
          processImageFile(pastedFile);
        }
        break;
      }
    }

    if (!imageFound) {
      showPasteError('No image found in clipboard. Please copy an image first.');
    }
  }

  function processImageFile(file: File) {
    const reader = new FileReader();

    reader.onload = function(e) {
      if (originalImage && e.target?.result) {
        originalImage.src = e.target.result as string;
        originalImage.onload = function() {
          if (canvas && ctx && originalImage) {
            // Set canvas size to match image
            canvas.width = originalImage.naturalWidth;
            canvas.height = originalImage.naturalHeight;

            // Draw the original image to canvas immediately
            ctx.drawImage(originalImage, 0, 0);

            // Show processing section
            document.getElementById('processing-section')?.classList.remove('hidden');

            // Scroll to processing section for better UX
            document.getElementById('processing-section')?.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        };
      }
    };

    reader.onerror = function() {
      showPasteError('Failed to read the image file');
    };

    reader.readAsDataURL(file);
  }

  function showPasteSuccess() {
    const statusDiv = document.getElementById('paste-status');
    const successDiv = document.getElementById('paste-success');
    const errorDiv = document.getElementById('paste-error');

    if (statusDiv && successDiv && errorDiv) {
      errorDiv.classList.add('hidden');
      successDiv.classList.remove('hidden');
      statusDiv.classList.remove('hidden');

      // Auto-hide success message after 3 seconds
      setTimeout(() => {
        hidePasteStatus();
      }, 3000);
    }
  }

  function showPasteError(message: string) {
    const statusDiv = document.getElementById('paste-status');
    const successDiv = document.getElementById('paste-success');
    const errorDiv = document.getElementById('paste-error');
    const errorMessage = document.getElementById('paste-error-message');

    if (statusDiv && successDiv && errorDiv && errorMessage) {
      successDiv.classList.add('hidden');
      errorMessage.textContent = message;
      errorDiv.classList.remove('hidden');
      statusDiv.classList.remove('hidden');

      // Auto-hide error message after 5 seconds
      setTimeout(() => {
        hidePasteStatus();
      }, 5000);
    }
  }

  function showPasteHint() {
    const pasteZone = document.getElementById('paste-zone');
    if (pasteZone) {
      pasteZone.classList.add('border-accent', 'bg-accent/10');

      // Remove hint after 2 seconds
      setTimeout(() => {
        pasteZone.classList.remove('border-accent', 'bg-accent/10');
      }, 2000);
    }
  }

  function hidePasteStatus() {
    const statusDiv = document.getElementById('paste-status');
    if (statusDiv) {
      statusDiv.classList.add('hidden');
    }
  }

  function initImageInverter() {
    canvas = document.getElementById('inverted-canvas') as HTMLCanvasElement;
    ctx = canvas?.getContext('2d') || null;
    originalImage = document.getElementById('original-image') as HTMLImageElement;

    const invertBtn = document.getElementById('invert-btn') as HTMLButtonElement;
    const flipHorizontalBtn = document.getElementById('flip-horizontal-btn') as HTMLButtonElement;
    const flipVerticalBtn = document.getElementById('flip-vertical-btn') as HTMLButtonElement;
    const downloadBtn = document.getElementById('download-btn') as HTMLButtonElement;
    const resetBtn = document.getElementById('reset-btn') as HTMLButtonElement;
    const pasteZone = document.getElementById('paste-zone') as HTMLElement;

    // Button event listeners
    invertBtn?.addEventListener('click', () => applyTransformation('invert'));
    flipHorizontalBtn?.addEventListener('click', () => applyTransformation('flipHorizontal'));
    flipVerticalBtn?.addEventListener('click', () => applyTransformation('flipVertical'));
    downloadBtn?.addEventListener('click', downloadImage);
    resetBtn?.addEventListener('click', resetTool);

    // Initialize clipboard paste functionality
    initClipboardPaste(pasteZone);
  }

  // Global function for file upload callback
  (window as any).handleImageUpload = function(file: File) {
    currentFile = file;
    processImageFile(file);
  };

  // Unified transformation function
  function applyTransformation(type: 'invert' | 'flipHorizontal' | 'flipVertical') {
    if (!originalImage?.src || !canvas || !ctx) return;

    const processingStatus = document.getElementById('processing-status');
    const buttons = document.querySelectorAll('#invert-btn, #flip-horizontal-btn, #flip-vertical-btn');

    // Show processing status
    processingStatus?.classList.remove('hidden');

    // Disable all transformation buttons during processing
    buttons.forEach(btn => {
      if (btn instanceof HTMLButtonElement) {
        btn.disabled = true;
        btn.style.opacity = '0.6';
      }
    });

    // Use setTimeout to allow UI to update
    setTimeout(() => {
      try {
        if (type === 'invert') {
          applyColorInversion();
        } else if (type === 'flipHorizontal') {
          applyHorizontalFlip();
        } else if (type === 'flipVertical') {
          applyVerticalFlip();
        }

      } catch (error) {
        console.error(`Error applying ${type} transformation:`, error);
        alert(`Error applying ${type} transformation. Please try again.`);
      } finally {
        // Hide processing status
        processingStatus?.classList.add('hidden');

        // Re-enable all transformation buttons
        buttons.forEach(btn => {
          if (btn instanceof HTMLButtonElement) {
            btn.disabled = false;
            btn.style.opacity = '1';
          }
        });
      }
    }, 100);
  }

  function applyColorInversion() {
    if (!ctx || !canvas) return;

    // Get current canvas content
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

    // Invert colors
    for (let i = 0; i < data.length; i += 4) {
      data[i] = 255 - data[i];     // Red
      data[i + 1] = 255 - data[i + 1]; // Green
      data[i + 2] = 255 - data[i + 2]; // Blue
      // Alpha channel (data[i + 3]) remains unchanged
    }

    // Put inverted image data back to canvas
    ctx.putImageData(imageData, 0, 0);
  }

  function applyHorizontalFlip() {
    if (!ctx || !canvas) return;

    // Create a temporary canvas to hold the current image
    const tempCanvas = document.createElement('canvas');
    const tempCtx = tempCanvas.getContext('2d');
    if (!tempCtx) return;

    // Set temp canvas size to match main canvas
    tempCanvas.width = canvas.width;
    tempCanvas.height = canvas.height;

    // Copy current canvas content to temp canvas
    tempCtx.drawImage(canvas, 0, 0);

    // Clear main canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Apply horizontal flip transformation
    ctx.save();
    ctx.scale(-1, 1);
    ctx.drawImage(tempCanvas, -canvas.width, 0);
    ctx.restore();
  }

  function applyVerticalFlip() {
    if (!ctx || !canvas) return;

    // Create a temporary canvas to hold the current image
    const tempCanvas = document.createElement('canvas');
    const tempCtx = tempCanvas.getContext('2d');
    if (!tempCtx) return;

    // Set temp canvas size to match main canvas
    tempCanvas.width = canvas.width;
    tempCanvas.height = canvas.height;

    // Copy current canvas content to temp canvas
    tempCtx.drawImage(canvas, 0, 0);

    // Clear main canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Apply vertical flip transformation
    ctx.save();
    ctx.scale(1, -1);
    ctx.drawImage(tempCanvas, 0, -canvas.height);
    ctx.restore();
  }

  function downloadImage() {
    if (!canvas) return;

    // Create download link
    const link = document.createElement('a');
    link.download = `inverted-${currentFile ? currentFile.name : 'image.png'}`;
    link.href = canvas.toDataURL();

    // Trigger download
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  function resetTool() {
    // Hide processing section
    document.getElementById('processing-section')?.classList.add('hidden');

    // Clear file input
    const fileInput = document.getElementById('image-upload') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }

    // Reset variables
    currentFile = null;

    // Clear canvas
    if (ctx && canvas) {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
    }

    // Clear original image
    if (originalImage) {
      originalImage.src = '';
    }

    // Hide paste status
    hidePasteStatus();

    // Re-enable all buttons and reset their state
    const buttons = document.querySelectorAll('#invert-btn, #flip-horizontal-btn, #flip-vertical-btn');
    buttons.forEach(btn => {
      if (btn instanceof HTMLButtonElement) {
        btn.disabled = false;
        btn.style.opacity = '1';
      }
    });
  }

  // Initialize on page load
  initImageInverter();

  // Re-initialize on view transitions
  document.addEventListener('astro:after-swap', initImageInverter);
</script>
