@layer base {
  .prose {
    /* Typography base styles - compatible with Tailwind v3 */
  }

  /* Headings */
  .prose h1,
  .prose h2,
  .prose h3,
  .prose h4,
  .prose h5,
  .prose h6 {
    @apply !mb-3 !text-foreground;
  }

  .prose h3 {
    @apply italic;
  }

  /* Paragraphs */
  .prose p {
    @apply !text-foreground;
  }

  /* Links */
  .prose a {
    @apply !text-foreground !decoration-dashed underline-offset-8 break-words;
  }

  .prose a:hover {
    @apply !text-accent;
  }

  /* Blockquotes */
  .prose blockquote {
    @apply opacity-80 break-words border-l-4 border-accent/50;
  }

  /* Figure captions */
  .prose figcaption {
    @apply !text-foreground opacity-70;
  }

  /* Strong text */
  .prose strong {
    @apply !text-foreground;
  }

  /* Code */
  .prose code {
    @apply rounded bg-muted/75 p-1 !text-foreground;
  }

  .prose code:before,
  .prose code:after {
    content: none !important;
  }

  /* Lists */
  .prose ol {
    @apply !text-foreground;
  }

  .prose ul {
    @apply overflow-x-clip !text-foreground;
  }

  .prose li::marker {
    @apply !text-accent;
  }

  /* Tables */
  .prose table {
    @apply text-foreground;
  }

  .prose th {
    @apply border border-border;
  }

  .prose td {
    @apply border border-border;
  }

  /* Images */
  .prose img {
    @apply mx-auto !my-2 border-2 border-border;
  }

  /* Horizontal rules */
  .prose hr {
    @apply !border-border;
  }
  .prose thead th:first-child,
  tbody td:first-child,
  tfoot td:first-child {
    padding-inline-start: 0.5714286em !important;
  }
  .prose h2#table-of-contents {
    @apply mb-2;
  }
  .prose details {
    @apply inline-block cursor-pointer text-foreground select-none;
  }
  .prose summary {
    @apply focus-visible:no-underline focus-visible:outline-2 focus-visible:outline-offset-1 focus-visible:outline-accent focus-visible:outline-dashed;
  }
  .prose h2#table-of-contents + p {
    @apply hidden;
  }

  /* ===== Code Blocks & Syntax Highlighting ===== */
  pre:has(code) {
    @apply border border-border;
  }

  .prose code {
    @apply break-words;
  }

  .prose table code {
    /* add line breaks whenever necessary for codes under table */
    @apply break-all sm:break-normal;
  }

  pre > code {
    white-space: pre;
  }

  /* Apply Dark Theme (if multi-theme specified) */
  html[data-theme="dark"] pre:has(code),
  html[data-theme="dark"] pre:has(code) span {
    color: var(--shiki-dark) !important;
    background-color: var(--shiki-dark-bg) !important;
    font-style: var(--shiki-dark-font-style) !important;
    font-weight: var(--shiki-dark-font-weight) !important;
    text-decoration: var(--shiki-dark-text-decoration) !important;
  }
}
