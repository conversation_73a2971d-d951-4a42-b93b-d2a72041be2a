---
// Feedback Form Component
import { getCurrentLanguage } from "@/i18n/config";
import { t, preloadTranslations } from "@/i18n/utils";

const currentLanguage = getCurrentLanguage(Astro.url.pathname);
await preloadTranslations(currentLanguage, ['feedback']);

// 获取翻译文本
const title = await t('title', currentLanguage, 'feedback');
const description = await t('description', currentLanguage, 'feedback');
const suggestionLabel = await t('form.suggestion_label', currentLanguage, 'feedback');
const suggestionPlaceholder = await t('form.suggestion_placeholder', currentLanguage, 'feedback');
const suggestionMinChars = await t('form.suggestion_min_chars', currentLanguage, 'feedback');
const emailLabel = await t('form.email_label', currentLanguage, 'feedback');
const emailOptional = await t('form.email_optional', currentLanguage, 'feedback');
const emailHelp = await t('form.email_help', currentLanguage, 'feedback');
const privacyAgreement = await t('form.privacy_agreement', currentLanguage, 'feedback');
const privacyPolicy = await t('form.privacy_policy', currentLanguage, 'feedback');
const submitButton = await t('form.submit_button', currentLanguage, 'feedback');
const privacyTitle = await t('privacy.title', currentLanguage, 'feedback');
const successMessage = await t('messages.success', currentLanguage, 'feedback');
const errorDefault = await t('messages.error_default', currentLanguage, 'feedback');
---

<div class="feedback-form-container">
  <div class="bg-background border border-border rounded-lg p-6 shadow-sm">
    <h3 class="text-xl font-bold text-accent mb-2">{title}</h3>
    <p class="text-sm text-foreground/70 mb-4">
      {description}
    </p>

    <!-- Feedback Form -->
    <form id="feedback-form" class="space-y-4">
      <!-- Suggestion Text Area -->
      <div>
        <label for="suggestion" class="block text-sm font-medium text-foreground mb-2">
          {suggestionLabel} <span class="text-red-500">*</span>
        </label>
        <textarea
          id="suggestion"
          name="suggestion"
          rows="4"
          placeholder={suggestionPlaceholder}
          class="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-accent focus:border-accent resize-none text-sm"
          required
          minlength="10"
          maxlength="500"
        ></textarea>
        <div class="flex justify-between text-xs text-foreground/60 mt-1">
          <span>{suggestionMinChars}</span>
          <span id="char-count">0/500</span>
        </div>
      </div>

      <!-- Email Field (Optional) -->
      <div>
        <label for="email" class="block text-sm font-medium text-foreground mb-2">
          {emailLabel} <span class="text-foreground/60">{emailOptional}</span>
        </label>
        <input
          type="email"
          id="email"
          name="email"
          placeholder="<EMAIL>"
          class="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-accent focus:border-accent text-sm"
        />
        <p class="text-xs text-foreground/60 mt-1">
          {emailHelp}
        </p>
      </div>

      <!-- Privacy Agreement -->
      <div class="space-y-2">
        <label class="flex items-start space-x-2 text-sm">
          <input
            type="checkbox"
            id="privacy-agreement"
            name="privacy-agreement"
            required
            class="mt-0.5 h-4 w-4 text-accent focus:ring-accent border-border rounded"
          />
          <span class="text-foreground/80">
            {privacyAgreement} <button type="button" id="privacy-toggle" class="text-accent hover:underline">{privacyPolicy}</button> <span class="text-red-500">*</span>
          </span>
        </label>

        <!-- Privacy Policy (Expandable) -->
        <div id="privacy-policy" class="hidden bg-muted/20 p-3 rounded text-xs text-foreground/70">
          <h4 class="font-medium mb-2">{privacyTitle}</h4>
          <ul class="space-y-1 list-disc list-inside" id="privacy-points">
            <!-- Privacy points will be populated by JavaScript -->
          </ul>
        </div>
      </div>

      <!-- Submit Button -->
      <button
        type="submit"
        id="submit-btn"
        class="w-full bg-accent text-background py-2 px-4 rounded-lg hover:bg-accent/90 transition-colors font-medium text-sm disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {submitButton}
      </button>
    </form>

    <!-- Status Messages -->
    <div id="form-status" class="mt-4 hidden">
      <div id="success-message" class="hidden bg-green-50 border border-green-200 text-green-800 px-3 py-2 rounded text-sm">
        ✅ <span id="success-text">{successMessage}</span>
      </div>
      <div id="error-message" class="hidden bg-red-50 border border-red-200 text-red-800 px-3 py-2 rounded text-sm">
        ❌ <span id="error-text">{errorDefault}</span>
      </div>
    </div>
  </div>
</div>

<script define:vars={{ currentLanguage }} is:inline>
  // 翻译数据
  const translations = {
    en: {
      privacyPoints: [
        "We promise to protect the confidentiality of your information",
        "We will never share or leak your data",
        "Email addresses are only used to reply to your suggestions (if provided)",
        "You can request deletion of your data at any time"
      ],
      submitting: "Submitting...",
      sendFeedback: "Send Feedback",
      errorMinLength: "Suggestion must be at least 10 characters long",
      errorPrivacyRequired: "Please agree to the privacy policy",
      errorNetwork: "Network error: {message}. Please check your connection and try again.",
      errorDefault: "Submission failed, please try again later"
    },
    de: {
      privacyPoints: [
        "Wir verpflichten uns, die Vertraulichkeit Ihrer Informationen zu schützen",
        "Wir werden Ihre Daten niemals weitergeben oder preisgeben",
        "E-Mail-Adressen werden nur verwendet, um auf Ihre Vorschläge zu antworten (falls angegeben)",
        "Sie können jederzeit die Löschung Ihrer Daten anfordern"
      ],
      submitting: "Wird gesendet...",
      sendFeedback: "Feedback senden",
      errorMinLength: "Der Vorschlag muss mindestens 10 Zeichen lang sein",
      errorPrivacyRequired: "Bitte stimmen Sie der Datenschutzrichtlinie zu",
      errorNetwork: "Netzwerkfehler: {message}. Bitte überprüfen Sie Ihre Verbindung und versuchen Sie es erneut.",
      errorDefault: "Übermittlung fehlgeschlagen, bitte versuchen Sie es später erneut"
    }
  };

  const t = translations[currentLanguage] || translations.en;

  function initFeedbackForm() {
    // 填充隐私政策点
    const privacyPointsList = document.getElementById('privacy-points');
    if (privacyPointsList && t.privacyPoints) {
      privacyPointsList.innerHTML = t.privacyPoints
        .map(point => `<li>${point}</li>`)
        .join('');
    }
    const form = document.getElementById('feedback-form');
    const suggestionTextarea = document.getElementById('suggestion');
    const charCount = document.getElementById('char-count');
    const privacyToggle = document.getElementById('privacy-toggle');
    const privacyPolicy = document.getElementById('privacy-policy');
    const submitBtn = document.getElementById('submit-btn');
    const formStatus = document.getElementById('form-status');
    const successMessage = document.getElementById('success-message');
    const errorMessage = document.getElementById('error-message');
    const errorText = document.getElementById('error-text');

    // Character count update
    suggestionTextarea?.addEventListener('input', () => {
      const length = suggestionTextarea.value.length;
      charCount.textContent = `${length}/500`;

      if (length < 10) {
        charCount.classList.add('text-red-500');
        charCount.classList.remove('text-foreground/60');
      } else {
        charCount.classList.remove('text-red-500');
        charCount.classList.add('text-foreground/60');
      }
    });

    // Privacy policy toggle
    privacyToggle?.addEventListener('click', (e) => {
      e.preventDefault();
      privacyPolicy.classList.toggle('hidden');
    });

    // Form submission
    form?.addEventListener('submit', async (e) => {
      e.preventDefault();

      const formData = new FormData(form);
      const suggestion = formData.get('suggestion');
      const email = formData.get('email');
      const privacyAgreement = formData.get('privacy-agreement');

      // Validation
      if (!suggestion || suggestion.length < 10) {
        showError(t.errorMinLength);
        return;
      }

      if (!privacyAgreement) {
        showError(t.errorPrivacyRequired);
        return;
      }

      // Show loading state
      submitBtn.disabled = true;
      submitBtn.textContent = t.submitting;
      hideMessages();

      try {
        // Submit to Supabase
        console.log('Submitting feedback...', {
          suggestion: suggestion.trim(),
          email: email.trim() || null,
          timestamp: new Date().toISOString(),
          user_agent: navigator.userAgent,
          page_url: window.location.href
        });

        const response = await fetch('/api/feedback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            suggestion: suggestion.trim(),
            email: email.trim() || null,
            timestamp: new Date().toISOString(),
            user_agent: navigator.userAgent,
            page_url: window.location.href
          })
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', Object.fromEntries(response.headers.entries()));

        if (response.ok) {
          const successData = await response.json();
          console.log('Success response:', successData);
          showSuccess();
          form.reset();
          charCount.textContent = '0/500';
        } else {
          const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
          console.error('Error response:', errorData);
          showError(errorData.message || t.errorDefault);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error('Feedback submission error details:', {
          message: errorMessage,
          stack: error instanceof Error ? error.stack : undefined,
          name: error instanceof Error ? error.name : 'Unknown',
          error: error
        });
        showError(t.errorNetwork.replace('{message}', errorMessage));
      } finally {
        submitBtn.disabled = false;
        submitBtn.textContent = t.sendFeedback;
      }
    });

    function showSuccess() {
      formStatus.classList.remove('hidden');
      successMessage.classList.remove('hidden');
      errorMessage.classList.add('hidden');
    }

    function showError(message) {
      formStatus.classList.remove('hidden');
      errorMessage.classList.remove('hidden');
      successMessage.classList.add('hidden');
      errorText.textContent = message;
    }

    function hideMessages() {
      formStatus.classList.add('hidden');
      successMessage.classList.add('hidden');
      errorMessage.classList.add('hidden');
    }
  }

  // Initialize on page load
  initFeedbackForm();

  // Re-initialize on view transitions
  document.addEventListener('astro:after-swap', initFeedbackForm);
</script>

<style>
  .feedback-form-container {
    position: sticky;
    top: 2rem;
  }
</style>
