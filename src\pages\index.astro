---
import { getCollection } from "astro:content";
import Layout from "@/layouts/Layout.astro";
import Header from "@/components/Header.astro";
import Footer from "@/components/Footer.astro";
import Socials from "@/components/Socials.astro";
import LinkButton from "@/components/LinkButton.astro";
import Card from "@/components/Card.astro";
import Hr from "@/components/Hr.astro";
import ImageInverter from "@/components/ImageInverter.astro";
import FeedbackForm from "@/components/FeedbackForm.astro";
import getSortedPosts from "@/utils/getSortedPosts";

import { SITE } from "@/config";
import { SOCIALS } from "@/constants";

const posts = await getCollection("blog");

const sortedPosts = getSortedPosts(posts);
const featuredPosts = sortedPosts.filter(({ data }) => data.featured);
const recentPosts = sortedPosts.filter(({ data }) => !data.featured);
---

<Layout
  title="Invert Colors Online - Free Image Color Inverter Tool"
  description="Transform your images instantly with our free online color inverter tool. Create stunning negative effects, improve accessibility, and process images locally in your browser for complete privacy."
>
  <Header />
  <main id="main-content" data-layout="index">
    <!-- Main Content Container with Sidebar -->
    <div class="container mx-auto px-4">
      <div class="flex flex-col lg:flex-row gap-8">
        <!-- Main Content Area -->
        <div class="flex-1 lg:max-w-4xl">
          <!-- Image Processing Tool Section - First Thing Users See -->
          <section id="image-black-white-tool" class="pt-8 pb-6">
            <div class="text-center mb-8">
              <h1 class="text-4xl font-bold tracking-wide mb-4 sm:text-5xl">🎨 Image Processing Tool</h1>
              <p class="text-lg text-foreground/80 max-w-2xl mx-auto">
                Transform your images instantly with multiple effects: invert colors, flip horizontally, flip vertically,
                and more. Create stunning effects, improve accessibility, or just have fun with your photos - all processed
                locally in your browser for complete privacy.
              </p>
            </div>

            <div class="max-w-4xl mx-auto">
              <ImageInverter />
            </div>

            <!-- Image Processing Features - Prominent Call-to-Action -->
            <div class="mt-8 text-center">
              <div class="inline-block bg-gradient-to-r from-accent to-accent/80 text-background px-8 py-4 rounded-lg shadow-lg">
                <h2 class="text-2xl font-bold mb-2">Transform Your Images Instantly</h2>
                <p class="text-background/90">Upload or paste your image above and use any of the four processing buttons: Invert Colors, Flip Horizontal, Flip Vertical, or Download!</p>
              </div>
            </div>

            <!-- Features -->
            <div class="mt-8 grid gap-4 md:grid-cols-3 max-w-3xl mx-auto">
              <div class="text-center p-4 border border-border rounded-lg">
                <div class="text-2xl mb-2">🔒</div>
                <h3 class="font-semibold mb-1">Privacy First</h3>
                <p class="text-sm text-foreground/70">All processing happens in your browser. No uploads to servers.</p>
              </div>
              <div class="text-center p-4 border border-border rounded-lg">
                <div class="text-2xl mb-2">⚡</div>
                <h3 class="font-semibold mb-1">Instant Results</h3>
                <p class="text-sm text-foreground/70">See your inverted image immediately with one click.</p>
              </div>
              <div class="text-center p-4 border border-border rounded-lg">
                <div class="text-2xl mb-2">💾</div>
                <h3 class="font-semibold mb-1">Easy Download</h3>
                <p class="text-sm text-foreground/70">Download your processed image in high quality.</p>
              </div>
            </div>
          </section>

          <Hr />

          <!-- About Section -->
          <section id="about" class="pt-12 pb-6">
            <div class="text-center mb-8">
              <h2 class="text-3xl font-bold tracking-wide mb-4">Welcome to Invert colors online</h2>
              <div class="max-w-3xl mx-auto space-y-4">
                <p class="text-lg text-foreground/80">
                  Your go-to collection of online tools and utilities for image processing and more.
                  Transform images, manipulate colors, and perform various tasks - all in your browser
                  with complete privacy and security.
                </p>
                <p class="text-foreground/80">
                  Try our featured Image Color Inverter tool above, or explore all
                  <LinkButton
                    class="underline decoration-dashed underline-offset-4 hover:text-accent"
                    href="/tools"
                  >
                    available tools
                  </LinkButton>.
                </p>
                {
                  // only display if at least one social link is enabled
                  SOCIALS.length > 0 && (
                    <div class="mt-6 flex flex-col sm:flex-row sm:items-center justify-center">
                      <div class="mr-2 mb-1 whitespace-nowrap sm:mb-0">Social Links:</div>
                      <Socials />
                    </div>
                  )
                }
              </div>
            </div>
          </section>

          <Hr />

          <!-- Additional Tools Section -->
          <section id="additional-tools" class="pt-12 pb-6">
            <h2 class="text-2xl font-semibold tracking-wide">More Tools & Features</h2>
            <div class="mt-6 grid gap-6 md:grid-cols-2">
              <div class="border border-border rounded-lg p-6 hover:bg-muted/10 transition-colors">
                <h3 class="text-xl font-semibold mb-3 text-accent">🖼️ Black & White Converter</h3>
                <p class="text-foreground/80 mb-4">
                  Convert your colorful images to elegant black and white or grayscale versions.
                  Perfect for professional photography and artistic effects.
                </p>
                <div class="flex flex-wrap gap-2 mb-4">
                  <span class="text-xs px-2 py-1 bg-accent/10 text-accent rounded">Coming Soon</span>
                  <span class="text-xs px-2 py-1 bg-accent/10 text-accent rounded">High Quality</span>
                  <span class="text-xs px-2 py-1 bg-accent/10 text-accent rounded">Multiple Formats</span>
                </div>
                <LinkButton
                  href="/tools"
                  class="inline-block px-4 py-2 border border-accent text-accent rounded hover:bg-accent hover:text-background transition-colors"
                >
                  View All Tools →
                </LinkButton>
              </div>

              <div class="border border-border rounded-lg p-6 hover:bg-muted/10 transition-colors">
                <h3 class="text-xl font-semibold mb-3">📚 Learn More</h3>
                <p class="text-foreground/80 mb-4">
                  Discover tutorials, tips, and guides about image processing, color theory,
                  and how to get the most out of our tools.
                </p>
                <div class="flex flex-wrap gap-2 mb-4">
                  <span class="text-xs px-2 py-1 bg-muted/20 text-foreground/60 rounded">Tutorials</span>
                  <span class="text-xs px-2 py-1 bg-muted/20 text-foreground/60 rounded">Tips & Tricks</span>
                  <span class="text-xs px-2 py-1 bg-muted/20 text-foreground/60 rounded">Guides</span>
                </div>
                <LinkButton
                  href="/posts"
                  class="inline-block px-4 py-2 border border-border text-foreground rounded hover:bg-muted/20 transition-colors"
                >
                  Read Articles →
                </LinkButton>
              </div>
            </div>
          </section>

          <Hr />

          {
            featuredPosts.length > 0 && (
              <>
                <section id="featured" class="pt-12 pb-6">
                  <h2 class="text-2xl font-semibold tracking-wide">Featured</h2>
                  <ul>
                    {featuredPosts.map(data => (
                      <Card variant="h3" {...data} />
                    ))}
                  </ul>
                </section>
                {recentPosts.length > 0 && <Hr />}
              </>
            )
          }

          {
            recentPosts.length > 0 && (
              <section id="recent-posts" class="pt-12 pb-6">
                <h2 class="text-2xl font-semibold tracking-wide">Recent Posts</h2>
                <ul>
                  {recentPosts.map(
                    (data, index) =>
                      index < SITE.postPerIndex && <Card variant="h3" {...data} />
                  )}
                </ul>
              </section>
            )
          }

          <div class="my-8 text-center">
            <LinkButton href="/posts/">
              All Posts
              <svg class="inline-block" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                <path d="M5 12l14 0" />
                <path d="M5 12l6 6" />
                <path d="M5 12l6 -6" />
              </svg>
            </LinkButton>
          </div>
        </div>

        <!-- Right Sidebar -->
        <aside class="lg:w-80 lg:flex-shrink-0">
          <div class="lg:sticky lg:top-8">
            <FeedbackForm />
          </div>
        </aside>
      </div>
    </div>
  </main>
  <Footer />
</Layout>

<script>
  document.addEventListener("astro:page-load", () => {
    const indexLayout = (document.querySelector("#main-content") as HTMLElement)
      ?.dataset?.layout;
    if (indexLayout) {
      sessionStorage.setItem("backUrl", "/");
    }
  });
</script>
