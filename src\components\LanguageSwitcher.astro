---
import { languages, getCurrentLanguage, getLocalizedUrl, type Language } from '@/i18n/config';

interface Props {
  pathname: string;
  class?: string;
}

const { pathname, class: className = '' } = Astro.props;
const currentLanguage = getCurrentLanguage(pathname);
---

<div class={`relative ${className}`}>
  <div class="relative inline-block text-left">
    <button
      type="button"
      class="inline-flex items-center justify-center w-full px-3 py-2 text-sm font-medium text-foreground bg-background border border-border rounded-md hover:bg-muted focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2 transition-colors"
      id="language-menu-button"
      aria-expanded="false"
      aria-haspopup="true"
    >
      <span class="mr-2 leading-none" data-language={currentLanguage}>
        {currentLanguage === 'en' ? '🇺🇸' : '🇩🇪'}
      </span>
      <span class="language-name">
        {languages[currentLanguage]}
      </span>
      <svg class="ml-2 -mr-1 h-4 w-4 transition-transform duration-200" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
      </svg>
    </button>

    <div
      class="language-menu absolute right-0 z-50 mt-2 w-48 origin-top-right rounded-md bg-background border border-border shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none hidden transform transition-all duration-200 ease-out opacity-0 scale-95 pointer-events-none"
      role="menu"
      aria-orientation="vertical"
      aria-labelledby="language-menu-button"
      tabindex="-1"
    >
      <div class="py-1" role="none">
        {Object.entries(languages).map(([lang, name]) => {
          const isActive = lang === currentLanguage;
          const localizedUrl = getLocalizedUrl(pathname, lang as Language);

          return (
            <a
              href={localizedUrl}
              class={`group flex items-center px-4 py-2 text-sm transition-colors ${
                isActive
                  ? 'bg-accent text-background font-medium'
                  : 'text-foreground hover:bg-muted hover:text-accent'
              }`}
              role="menuitem"
              tabindex="-1"
              data-language={lang}
            >
              <span class="mr-3 leading-none">
                {lang === 'en' ? '🇺🇸' : '🇩🇪'}
              </span>
              <span class="language-name">{name}</span>
              {isActive && (
                <svg class="ml-auto h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              )}
            </a>
          );
        })}
      </div>
    </div>
  </div>
</div>

<script>
  function initLanguageSwitcher() {
    const button = document.getElementById('language-menu-button');
    const menu = document.querySelector('.language-menu') as HTMLElement | null;

    if (!button || !menu) return;

    const chevron = button.querySelector('svg');

    // 切换菜单显示/隐藏
    function toggleMenu() {
      const isHidden = menu.classList.contains('hidden');

      if (isHidden) {
        menu.classList.remove('hidden', 'opacity-0', 'scale-95', 'pointer-events-none');
        menu.classList.add('opacity-100', 'scale-100');
        button.setAttribute('aria-expanded', 'true');
        chevron?.classList.add('rotate-180');
      } else {
        menu.classList.add('hidden', 'opacity-0', 'scale-95', 'pointer-events-none');
        menu.classList.remove('opacity-100', 'scale-100');
        button.setAttribute('aria-expanded', 'false');
        chevron?.classList.remove('rotate-180');
      }
    }

    // 关闭菜单
    function closeMenu() {
      menu.classList.add('hidden', 'opacity-0', 'scale-95', 'pointer-events-none');
      menu.classList.remove('opacity-100', 'scale-100');
      button.setAttribute('aria-expanded', 'false');
      chevron?.classList.remove('rotate-180');
    }

    // 按钮点击事件
    button.addEventListener('click', (e) => {
      e.stopPropagation();
      toggleMenu();
    });

    // 点击外部关闭菜单
    document.addEventListener('click', (e) => {
      if (!button.contains(e.target as Node) && !menu.contains(e.target as Node)) {
        closeMenu();
      }
    });

    // ESC键关闭菜单
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        closeMenu();
      }
    });

    // 语言切换时保存偏好
    const languageLinks = menu.querySelectorAll('a[data-language]');
    languageLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        const language = (e.currentTarget as HTMLElement).dataset.language;
        if (language) {
          localStorage.setItem('preferred-language', language);
        }
      });
    });
  }

  // 初始化
  initLanguageSwitcher();

  // 页面切换后重新初始化
  document.addEventListener('astro:after-swap', initLanguageSwitcher);
</script>


